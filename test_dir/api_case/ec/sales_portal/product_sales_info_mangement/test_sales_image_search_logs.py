# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from datetime import datetime, timedelta

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesImageSearchLogs(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_image_search_logs(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-查询图片搜索日志页面数据（Search Type默认是All）"""
        search_log_list = CentralIm().search_log_list(headers=sales_header)
        assert len(search_log_list["object"]["data"]) > 0, f'查询图片搜索日志页面数据异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_search_type')
    def test_search_log_list_search_type(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试search_type字段（多个查询类型）"""
        # 测试 All 类型（不传参数）
        search_log_list_all = CentralIm().search_log_list(
            headers=sales_header,
            search_type=None
        )
        assert search_log_list_all["success"] is True, f'search_type字段(All)测试失败{search_log_list_all}'
        assert "data" in search_log_list_all["object"], f'返回数据格式异常{search_log_list_all}'

        # 测试 Image 类型
        search_log_list_image = CentralIm().search_log_list(
            headers=sales_header,
            search_type="image"
        )
        assert search_log_list_image["success"] is True, f'search_type字段(image)测试失败{search_log_list_image}'
        assert "data" in search_log_list_image["object"], f'返回数据格式异常{search_log_list_image}'

        # 测试 UPC Code 类型
        search_log_list_upc = CentralIm().search_log_list(
            headers=sales_header,
            search_type="upc_code"
        )
        assert search_log_list_upc["success"] is True, f'search_type字段(upc_code)测试失败{search_log_list_upc}'
        assert "data" in search_log_list_upc["object"], f'返回数据格式异常{search_log_list_upc}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_user_id')
    def test_search_log_list_user_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试user_id字段"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            user_id="8903000"
        )
        assert search_log_list["success"] is True, f'user_id字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_log_list_time_range')
    def test_search_log_list_time_range(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试start_time和end_time时间区间查询"""
        # 设置时间区间：当前时间减7天到当前时间
        start_time = int((datetime.now() - timedelta(days=7)).timestamp())
        end_time = int(datetime.now().timestamp())

        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            start_time=start_time,
            end_time=end_time
        )
        assert search_log_list["success"] is True, f'时间区间查询测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'


